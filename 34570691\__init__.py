import json
import time
from datetime import datetime, date, timedelta
from typing import Dict, Set, Optional

from aqt import mw, gui_hooks
from aqt.qt import *
from anki.hooks import addHook
from anki.cards import Card

class DailyProgressBar:
    def __init__(self):
        self.progress_bar = None
        self.progress_label = None
        self.progress_widget = None
        self.deck_progress: Dict[int, Dict[str, Set[int]]] = {}
        self.current_deck_id = None
        self.current_date = None
        self.config = None
        self.load_config()
        
    def load_config(self):
        """Load configuration with defaults"""
        default_config = {
            "textColor": "#2196F3",
            "backgroundColor": "rgba(240, 240, 240, 255)",
            "foregroundColor": "#4CAF50",
            "borderRadius": 5,
            "height": 20,
            "showRemaining": True
        }
        self.config = mw.addonManager.getConfig(__name__) or default_config
        for key, value in default_config.items():
            if key not in self.config:
                self.config[key] = value
        
    def setup_progress_bar(self):
        """Create and setup the progress bar widget"""
        try:
            if not mw or not mw.centralWidget():
                return
                
            self.progress_widget = QWidget()
            layout = QHBoxLayout(self.progress_widget)
            layout.setContentsMargins(10, 5, 10, 5)
            
            self.progress_label = QLabel("Daily Progress: Loading...")
            self.progress_label.setStyleSheet(f"font-weight: bold; color: {self.config['textColor']};")
            
            self.progress_bar = QProgressBar()
            self.progress_bar.setMinimum(0)
            self.progress_bar.setMaximum(100)
            self.progress_bar.setValue(0)
            self.update_progress_bar_style()
            
            layout.addWidget(self.progress_label)
            layout.addWidget(self.progress_bar, 1)
            
            main_layout = mw.centralWidget().layout()
            if main_layout:
                main_layout.addWidget(self.progress_widget)
            else:
                main_layout = QVBoxLayout(mw.centralWidget())
                main_layout.addWidget(self.progress_widget)
                
        except Exception as e:
            print(f"Error setting up progress bar: {e}")
    
    def update_progress_bar_style(self):
        """Update progress bar stylesheet based on config"""
        height = self.config.get('height', 20)
        bg_color = self.config.get('backgroundColor', 'rgba(240, 240, 240, 255)')
        fg_color = self.config.get('foregroundColor', '#4CAF50')
        border_radius = self.config.get('borderRadius', 5)
        
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid #cccccc;
                border-radius: {border_radius}px;
                background-color: {bg_color};
                text-align: center;
                height: {height}px;
                color: black;
                font-weight: bold;
            }}
            QProgressBar::chunk {{
                background-color: {fg_color};
                border-radius: {max(0, border_radius - 2)}px;
            }}
        """)
    
    def get_deck_due_cards(self, deck_id: int) -> Set[int]:
        """Get all cards that are due today for a specific deck"""
        try:
            if not mw.col:
                return set()
                
            today = mw.col.sched.today
            
            deck_ids = [deck_id]
            try:
                deck_name = mw.col.decks.name(deck_id)
                for did, deck in mw.col.decks.decks.items():
                    if deck['name'].startswith(deck_name + '::'):
                        deck_ids.append(int(did))
            except:
                pass
            
            deck_ids_str = ','.join(map(str, deck_ids))
            
            # Get review cards due today or overdue
            review_ids = mw.col.db.list(f"""
                SELECT id FROM cards 
                WHERE did IN ({deck_ids_str})
                AND type = 2 
                AND due <= ?
                AND queue != -1
            """, today)
            
            # Get ALL learning cards (including those on cooldown)
            # This includes cards with queue=1 or queue=3 (learning/relearning)
            learning_ids = mw.col.db.list(f"""
                SELECT id FROM cards 
                WHERE did IN ({deck_ids_str})
                AND queue IN (1, 3)
            """)
            
            # Get new cards
            new_card_ids = mw.col.db.list(f"""
                SELECT id FROM cards 
                WHERE did IN ({deck_ids_str})
                AND type = 0
                AND queue = 0
            """)
            
            all_card_ids = set(review_ids + learning_ids + new_card_ids)
            print(f"Deck {deck_id}: {len(review_ids)} review, {len(learning_ids)} learning (including cooldown), {len(new_card_ids)} new")
            return all_card_ids
            
        except Exception as e:
            print(f"Error getting deck due cards: {e}")
            return set()
    
    def initialize_deck_progress(self, deck_id: int):
        """Initialize or update deck progress data"""
        try:
            current_due = self.get_deck_due_cards(deck_id)
            
            if deck_id not in self.deck_progress:
                # First time seeing this deck today
                self.deck_progress[deck_id] = {
                    'due_cards': current_due,
                    'completed_cards': set(),
                    'initial_count': len(current_due),
                    'seen_cards': current_due.copy()
                }
                print(f"Initialized deck {deck_id} with {len(current_due)} cards")
            else:
                # Update existing deck data - PRESERVE completed cards!
                existing_data = self.deck_progress[deck_id]
                
                # Find newly added cards
                new_cards = current_due - existing_data['seen_cards']
                if new_cards:
                    print(f"Found {len(new_cards)} new cards added to deck {deck_id}")
                    existing_data['seen_cards'].update(new_cards)
                
                # Update due cards
                existing_data['due_cards'] = current_due
                
                # IMPORTANT: Only keep completed cards that still exist in the deck
                # But DON'T reset completed cards that are still valid
                existing_completed = existing_data['completed_cards']
                valid_completed = existing_completed.intersection(existing_data['seen_cards'])
                existing_data['completed_cards'] = valid_completed
                
                print(f"Deck {deck_id}: {len(valid_completed)} completed cards preserved")
                
                # Update initial count if cards were added
                if len(existing_data['seen_cards']) > existing_data['initial_count']:
                    existing_data['initial_count'] = len(existing_data['seen_cards'])
                
            self.save_daily_state()
            
        except Exception as e:
            print(f"Error initializing deck progress: {e}")
    
    def get_current_deck_id(self) -> Optional[int]:
        """Get the currently selected deck ID"""
        try:
            if not mw.col:
                return None
            
            # Check if we're in review mode
            if mw.state == "review" and mw.reviewer.card:
                return mw.reviewer.card.did
            
            # Otherwise use the selected deck
            current = mw.col.decks.current()
            return current['id'] if current else None
            
        except Exception as e:
            print(f"Error getting current deck ID: {e}")
            return None
    
    def load_daily_state(self):
        """Load today's progress state from profile"""
        try:
            today_str = date.today().isoformat()
            
            if self.current_date != today_str:
                self.current_date = today_str
                self.deck_progress = {}
                self.save_daily_state()
            else:
                config = mw.addonManager.getConfig(__name__) or {}
                daily_data = config.get('daily_progress', {})
                today_data = daily_data.get(today_str, {})
                
                for deck_id_str, deck_data in today_data.items():
                    if deck_id_str.isdigit():
                        deck_id = int(deck_id_str)
                        self.deck_progress[deck_id] = {
                            'due_cards': set(deck_data.get('due_cards', [])),
                            'completed_cards': set(deck_data.get('completed_cards', [])),
                            'initial_count': deck_data.get('initial_count', 0),
                            'seen_cards': set(deck_data.get('seen_cards', []))
                        }
            
        except Exception as e:
            print(f"Error loading daily state: {e}")
    
    def refresh_current_deck(self):
        """Refresh the current deck's due cards"""
        try:
            deck_id = self.get_current_deck_id()
            if not deck_id:
                return
                
            self.current_deck_id = deck_id
            self.initialize_deck_progress(deck_id)
            
        except Exception as e:
            print(f"Error refreshing current deck: {e}")
    
    def save_daily_state(self):
        """Save current progress state to profile"""
        try:
            config = mw.addonManager.getConfig(__name__) or {}
            daily_data = config.get('daily_progress', {})
            
            today_str = date.today().isoformat()
            today_data = {}
            
            for deck_id, progress in self.deck_progress.items():
                today_data[str(deck_id)] = {
                    'due_cards': list(progress['due_cards']),
                    'completed_cards': list(progress['completed_cards']),
                    'initial_count': progress.get('initial_count', len(progress['due_cards'])),
                    'seen_cards': list(progress.get('seen_cards', progress['due_cards']))
                }
            
            daily_data[today_str] = today_data
            
            cutoff_date = (date.today() - timedelta(days=7)).isoformat()
            daily_data = {k: v for k, v in daily_data.items() if k >= cutoff_date}
            
            config['daily_progress'] = daily_data
            mw.addonManager.writeConfig(__name__, config)
            
        except Exception as e:
            print(f"Error saving daily state: {e}")
    
    def update_progress(self):
        """Update the progress bar display"""
        try:
            if not self.progress_bar or not self.progress_label:
                return
                
            if not self.current_deck_id or self.current_deck_id not in self.deck_progress:
                self.refresh_current_deck()
                
            if not self.current_deck_id or self.current_deck_id not in self.deck_progress:
                return
                
            deck_data = self.deck_progress[self.current_deck_id]
            
            # Use the maximum count we've seen for this deck today
            total_cards = max(
                len(deck_data.get('seen_cards', [])),
                deck_data.get('initial_count', 0),
                len(deck_data['due_cards'])
            )
            completed_cards = len(deck_data['completed_cards'])
            
            deck_name = "Unknown Deck"
            try:
                if mw.col and self.current_deck_id:
                    full_deck_name = mw.col.decks.name(self.current_deck_id)
                    deck_name = full_deck_name.split("::")[-1]
                    if len(deck_name) > 20:
                        deck_name = deck_name[:17] + "..."
            except:
                deck_name = f"Deck {self.current_deck_id}"
            
            if total_cards == 0:
                percentage = 100
                self.progress_label.setText(f"{deck_name}: All done! 🎉")
            else:
                percentage = int((completed_cards / total_cards) * 100)
                remaining = total_cards - completed_cards
                if self.config.get('showRemaining', True):
                    self.progress_label.setText(f"{deck_name}: {completed_cards}/{total_cards} ({percentage}%) - {remaining} remaining")
                else:
                    self.progress_label.setText(f"{deck_name}: {completed_cards}/{total_cards} ({percentage}%)")
            
            self.progress_bar.setValue(percentage)
            
            # Dynamic color based on progress
            if percentage == 100:
                color = "#4CAF50"  # Green
            elif percentage >= 75:
                color = "#2196F3"  # Blue
            elif percentage >= 50:
                color = "#FF9800"  # Orange
            else:
                color = "#F44336"  # Red
            
            # Update chunk color dynamically
            self.progress_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 2px solid #cccccc;
                    border-radius: {self.config.get('borderRadius', 5)}px;
                    background-color: {self.config.get('backgroundColor', 'rgba(240, 240, 240, 255)')};
                    text-align: center;
                    height: {self.config.get('height', 20)}px;
                    color: black;
                    font-weight: bold;
                }}
                QProgressBar::chunk {{
                    background-color: {color};
                    border-radius: {max(0, self.config.get('borderRadius', 5) - 2)}px;
                }}
            """)
            
        except Exception as e:
            print(f"Error updating progress: {e}")
    
    def on_card_answered(self, reviewer, card: Card, ease: int):
        """Called when a card is answered"""
        try:
            card_deck_id = card.did
            
            # Initialize deck if we haven't seen it yet
            if card_deck_id not in self.deck_progress:
                self.initialize_deck_progress(card_deck_id)
            
            deck_data = self.deck_progress[card_deck_id]
            
            if card.id in deck_data['due_cards']:
                card_completed = False
                today = mw.col.sched.today
                
                # Check if card is completed for today
                if card.type == 2:  # Review card
                    if card.due > today:
                        card_completed = True
                elif card.queue == 2:  # Graduated from learning
                    if card.due > today:
                        card_completed = True
                elif card.queue in (1, 3):  # Learning/relearning
                    # Check if it's scheduled for tomorrow or later
                    if card.due <= 86400:  # Day-based scheduling
                        if card.due > today:
                            card_completed = True
                    # Cards with timestamp-based scheduling (due > 86400) are still due today
                
                if card_completed:
                    deck_data['completed_cards'].add(card.id)
                    self.save_daily_state()
            
            if card_deck_id != self.current_deck_id:
                self.current_deck_id = card_deck_id
                
            self.update_progress()
                
        except Exception as e:
            print(f"Error processing card answer: {e}")
    
    def validate_and_update(self):
        """Validate completed cards and update progress"""
        try:
            if not self.current_deck_id or self.current_deck_id not in self.deck_progress:
                return
                
            deck_data = self.deck_progress[self.current_deck_id]
            validated_completed = set()
            today = mw.col.sched.today
            
            for card_id in deck_data['completed_cards']:
                try:
                    card = mw.col.get_card(card_id)
                    # Card is completed if scheduled for tomorrow or later
                    if card.type == 2 and card.due > today:
                        validated_completed.add(card_id)
                    elif card.queue == 2 and card.due > today:
                        validated_completed.add(card_id)
                    elif card.queue in (1, 3) and card.due <= 86400 and card.due > today:
                        validated_completed.add(card_id)
                except:
                    pass
            
            if len(validated_completed) != len(deck_data['completed_cards']):
                deck_data['completed_cards'] = validated_completed
                self.save_daily_state()
                self.update_progress()
                
        except Exception as e:
            print(f"Error validating cards: {e}")
    
    def on_main_window_init(self):
        """Initialize when main window is ready"""
        try:
            self.setup_progress_bar()
            self.load_daily_state()
            self.update_progress()
        except Exception as e:
            print(f"Error initializing progress bar: {e}")
    
    def on_state_change(self, new_state: str, old_state: str):
        """Handle Anki state changes"""
        try:
            print(f"State change: {old_state} -> {new_state}")
            
            if new_state == "deckBrowser":
                self.current_deck_id = None
                self.refresh_current_deck()
            elif new_state in ["overview", "review"]:
                self.refresh_current_deck()
                self.validate_and_update()
            
            self.update_progress()
            
        except Exception as e:
            print(f"Error on state change: {e}")
    
    def cleanup(self):
        """Clean up widget when addon is unloaded"""
        try:
            if self.progress_widget:
                self.progress_widget.setParent(None)
                self.progress_widget.deleteLater()
                self.progress_widget = None
        except Exception as e:
            print(f"Error in cleanup: {e}")


# Global instance
progress_tracker = DailyProgressBar()

# Hook functions
def init_progress_bar():
    try:
        progress_tracker.on_main_window_init()
    except Exception as e:
        print(f"Error in init_progress_bar: {e}")

def on_card_answered(reviewer, card, ease):
    try:
        progress_tracker.on_card_answered(reviewer, card, ease)
    except Exception as e:
        print(f"Error in on_card_answered: {e}")

def on_profile_loaded():
    try:
        progress_tracker.load_daily_state()
        progress_tracker.update_progress()
    except Exception as e:
        print(f"Error in on_profile_loaded: {e}")

def on_state_did_change(new_state, old_state):
    try:
        progress_tracker.on_state_change(new_state, old_state)
    except Exception as e:
        print(f"Error in on_state_did_change: {e}")

def periodic_update():
    """Periodic validation and update"""
    try:
        progress_tracker.validate_and_update()
        QTimer.singleShot(1000, periodic_update)
    except Exception as e:
        print(f"Error in periodic_update: {e}")

# Register hooks using both old and new APIs
try:
    # New API hooks
    gui_hooks.main_window_did_init.append(init_progress_bar)
    gui_hooks.reviewer_did_answer_card.append(on_card_answered)
    gui_hooks.profile_did_open.append(on_profile_loaded)
    
    # Old API hook for state changes
    addHook("afterStateChange", on_state_did_change)
    
    # Start periodic validation
    gui_hooks.main_window_did_init.append(lambda: QTimer.singleShot(2000, periodic_update))
    
    print("Daily Progress Bar addon loaded successfully")
    
except Exception as e:
    print(f"Error registering hooks: {e}")

# Cleanup on addon unload
def cleanup():
    try:
        progress_tracker.cleanup()
    except Exception as e:
        print(f"Error in cleanup: {e}")

# Set web exports if needed
try:
    mw.addonManager.setWebExports(__name__, r".*\.(css|js)")
except Exception as e:
    print(f"Error setting web exports: {e}")